<?php
defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Cron para análise de triagem DIANA
 *
 * Este script deve ser executado via crontab do sistema
 * Exemplo de configuração no crontab: Inserir a barra no primeiro campo da cron*/
// */30 * * * * php /var/www/gestaotarifaria/production/current/index.php cli/cron_analise_diana >> /var/log/diana_cron.log 2>&1

class Cron_analise_diana extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        // Verificar se está sendo executado via CLI e permitir apenas no ambiente de desenvolvimento
        if (!$this->input->is_cli_request() && ENVIRONMENT !== 'development') {
            show_error('Recurso não permitido', 403);
        }
    }

    /**
     * Método principal da cron
     */
    public function index()
    {
        $this->load->library('benchmark');
        $this->benchmark->mark('diana_start');

        $this->debug('=== INICIANDO ANÁLISE DIANA ===');
        $this->debug('Data/Hora: ' . date('Y-m-d H:i:s'));

        $this->load->model('item_model');
        $this->load->model('empresa_model');
        $this->load->library('Diana_api');

        // Verificar se a API está configurada
        if (!$this->diana_api->is_configurada()) {
            $this->debug('ERRO: API DIANA não está configurada corretamente');
            $this->debug('Verifique a configuração diana_api_url no config.php');
            return;
        }


        $this->debug('API DIANA configurada: ' . $this->config->item('diana_api_url'));

        $empresas = $this->empresa_model->get_empresas_with_status_triagem_diana();

        if (empty($empresas)) {
            $this->debug('Nenhuma empresa encontrada com funcionalidade status_triagem_diana ativa');
            return;
        }

        $this->debug('Encontradas ' . count($empresas) . ' empresas com funcionalidade ativa');

        $processados_total = 0;
        $sucessos_total = 0;
        $erros_total = 0;
        $empresas_processadas = [];

        // Para cada empresa, buscar itens para análise passando o id_empresa
        foreach ($empresas as $empresa) {
            $itens = $this->item_model->get_itens_para_triagem_diana_pendente($empresa->id_empresa);

            $itens_reprovados = $this->item_model->get_itens_para_triagem_diana_reprovada($empresa->id_empresa);

            if (empty($itens) && empty($itens_reprovados)) {
                $this->debug("Nenhum item encontrado para análise DIANA na empresa {$empresa->nome_fantasia} (ID: {$empresa->id_empresa})");
                continue;
            }

            $total_itens = count($itens) + count($itens_reprovados);
            $this->debug("Encontrados {$total_itens} itens para análise na empresa {$empresa->nome_fantasia} (ID: {$empresa->id_empresa})");
            $this->debug("  - Pendentes (passo 1): " . count($itens));
            $this->debug("  - Com respostas (passo 2): " . count($itens_reprovados));

            $empresas_processadas[] = $empresa->id_empresa;

            // PASSO 1: Processar itens pendentes (status 6, triagem pendente)
            foreach ($itens as $item) {
                $this->debug("Processando: {$item->part_number} (Empresa: {$item->id_empresa}, Estabelecimento: {$item->estabelecimento})");

                try {
                    // Chamar API DIANA
                    $resposta_api = $this->diana_api->analisar_triagem(
                        $item->funcao,
                        $item->aplicacao,
                        $item->material_constitutivo,
                        $item->descricao
                    );
                    
                    if ($resposta_api === false) {
                        $this->debug("  ✗ Falha na chamada da API");
                        $erros_total++;
                        continue;
                    }

                    // Processar resposta
                    $resultado = $this->diana_api->processar_resposta($resposta_api);

                    // Atualizar item com resultado
                    $sucesso = $this->item_model->processar_resultado_diana($item, $resultado);

                    if ($sucesso) {
                        $status_triagem = $resultado['triagem_aprovada'] ? 'aprovada' : 'reprovada';
                        $this->debug("  ✓ Triagem {$status_triagem}");

                        if (!$resultado['triagem_aprovada']) {
                            $this->debug("    Perguntas inseridas: " . count($resultado['perguntas_faltantes']));
                        }

                        $sucessos_total++;
                    } else {
                        $this->debug("  ✗ Falha ao processar resultado");
                        $erros_total++;
                    }
                } catch (Exception $e) {
                    $this->debug("  ✗ Exceção: " . $e->getMessage());
                    log_message('error', "DIANA Cron: Erro ao processar item {$item->part_number}: " . $e->getMessage());
                    // Detalhes da origem da exceção, se possível
                    if (method_exists($e, 'getTraceAsString')) {
                        $this->debug("    Rastreamento: " . $e->getTraceAsString());
                    }
                    $erros_total++;
                }

                $processados_total++;

                // Pequena pausa para não sobrecarregar a API
                usleep(100000); // 0.1 segundo
            }

            // PASSO 2: Processar itens com respostas (status 8, triagem reprovada)
            foreach ($itens_reprovados as $item) {
                $this->debug("Processando item com respostas: {$item->part_number} (Empresa: {$item->id_empresa}, Estabelecimento: {$item->estabelecimento})");

                try {
                    // Chamar API DIANA incluindo perguntas e respostas
                    $resposta_api = $this->diana_api->analisar_triagem_com_respostas(
                        $item->funcao,
                        $item->aplicacao,
                        $item->material_constitutivo,
                        $item->descricao,
                        $item->perguntas_respostas
                    );

                    if ($resposta_api === false) {
                        $this->debug("  ✗ Falha na chamada da API (com respostas)");
                        $erros_total++;
                        continue;
                    }

                    // Processar resposta
                    $resultado = $this->diana_api->processar_resposta($resposta_api);

                    // Atualizar item com resultado (passo 2)
                    $sucesso = $this->item_model->processar_resultado_diana_com_respostas($item, $resultado);

                    if ($sucesso) {
                        $status_triagem = $resultado['triagem_aprovada'] ? 'aprovada' : 'falha na triagem';
                        $this->debug("  ✓ Triagem {$status_triagem} (com respostas)");

                        if (!$resultado['triagem_aprovada']) {
                            $this->debug("    Status final: Falha na triagem (evita loop infinito)");
                        }

                        $sucessos_total++;
                    } else {
                        $this->debug("  ✗ Falha ao processar resultado (com respostas)");
                        $erros_total++;
                    }
                } catch (Exception $e) {
                    $this->debug("  ✗ Exceção (com respostas): " . $e->getMessage());
                    log_message('error', "DIANA Cron: Erro ao processar item com respostas {$item->part_number}: " . $e->getMessage());
                    if (method_exists($e, 'getTraceAsString')) {
                        $this->debug("    Rastreamento: " . $e->getTraceAsString());
                    }
                    $erros_total++;
                }

                $processados_total++;

                // Pequena pausa para não sobrecarregar a API
                usleep(100000); // 0.1 segundo
            }
        }

        $this->benchmark->mark('diana_end');
        $elapsed_time = $this->benchmark->elapsed_time('diana_start', 'diana_end');

        $this->debug('=== ANÁLISE DIANA FINALIZADA ===');
        $this->debug("Empresas processadas: " . count($empresas_processadas));
        $this->debug("Itens processados: {$processados_total}");
        $this->debug("Sucessos: {$sucessos_total}");
        $this->debug("Erros: {$erros_total}");
        $this->debug("Tempo total: {$elapsed_time} segundos");
        $this->debug("Taxa de sucesso: " . ($processados_total > 0 ? round(($sucessos_total / $processados_total) * 100, 2) : 0) . "%");

        // Log de resumo
        log_message('info', "DIANA Cron executada: {$processados_total} itens processados, {$sucessos_total} sucessos, {$erros_total} erros em {$elapsed_time}s");
    }

    /**
     * Método para debug/log
     */
    private function debug($message)
    {
        // Se executado via navegador exibir na tela
        if (!$this->input->is_cli_request()) {
            echo "<pre>" . $message . "</pre>";
            return;
        }

        echo "[" . date('Y-m-d H:i:s') . "] " . $message . PHP_EOL;
    }

    /**
     * Método para testar a configuração
     */
    public function test_config()
    {
        $this->debug('=== TESTE DE CONFIGURAÇÃO DIANA ===');

        $this->load->library('Diana_api');

        if ($this->diana_api->is_configurada()) {
            $this->debug('✓ API DIANA está configurada');
            $this->debug('URL: ' . $this->config->item('diana_api_url'));
        } else {
            $this->debug('✗ API DIANA não está configurada');
            $this->debug('URL atual: ' . ($this->config->item('diana_api_url') ?: 'Não definida'));
        }

        $this->load->model('item_model');
        $itens = $this->item_model->get_itens_para_triagem_diana();
        $this->debug('Itens encontrados para análise: ' . count($itens));

        if (count($itens) > 0) {
            $this->debug('Exemplo de item:');
            $item = $itens[0];
            $this->debug("  Part Number: {$item->part_number}");
            $this->debug("  Empresa: {$item->id_empresa}");
            $this->debug("  Função: " . substr($item->funcao, 0, 50) . "...");
            $this->debug("  Aplicação: " . substr($item->aplicacao, 0, 50) . "...");
            $this->debug("  Material: " . substr($item->material_constitutivo, 0, 50) . "...");
        }
    }

    /**
     * Método para processar apenas um item específico (para testes)
     */
    public function test_item($part_number = null, $id_empresa = null, $estabelecimento = '')
    {
        if (!$part_number || !$id_empresa) {
            $this->debug('Uso: php index.php cli/cron_analise_diana test_item [part_number] [id_empresa] [estabelecimento]');
            return;
        }

        $this->debug('=== TESTE DE ITEM ESPECÍFICO ===');
        $this->debug("Part Number: {$part_number}");
        $this->debug("Empresa: {$id_empresa}");
        $this->debug("Estabelecimento: {$estabelecimento}");

        $this->load->model('item_model');
        $this->load->library('Diana_api');

        // Buscar o item específico
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $item = $this->db->get('item')->row();

        if (!$item) {
            $this->debug('✗ Item não encontrado');
            return;
        }

        $this->debug('✓ Item encontrado');
        $this->debug("Status atual: {$item->id_status}");
        $this->debug("Status triagem DIANA: {$item->status_triagem_diana}");

        if (!$this->diana_api->is_configurada()) {
            $this->debug('✗ API DIANA não está configurada');
            return;
        }

        try {
            $resposta_api = $this->diana_api->analisar_triagem(
                $item->funcao,
                $item->aplicacao,
                $item->material_constitutivo,
                $item->descricao
            );

            if ($resposta_api === false) {
                $this->debug('✗ Falha na chamada da API');
                return;
            }

            $this->debug('✓ API respondeu com sucesso');
            $this->debug('Resposta: ' . json_encode($resposta_api, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            $resultado = $this->diana_api->processar_resposta($resposta_api);
            $this->debug('Resultado processado: ' . json_encode($resultado, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        } catch (Exception $e) {
            $this->debug('✗ Erro: ' . $e->getMessage());
        }
    }

    /**
     * Método para processar apenas uma empresa específica (para testes)
     */
    public function test_empresa($id_empresa = null)
    {
        if (!$id_empresa) {
            $this->debug('Uso: php index.php cli/cron_analise_diana test_empresa [id_empresa]');
            return;
        }

        $this->debug('=== TESTE DE EMPRESA ESPECÍFICA ===');
        $this->debug("Empresa: {$id_empresa}");

        $this->load->model('item_model');
        $this->load->library('Diana_api');

        // Buscar os itens da empresa
        $itens = $this->item_model->get_itens_para_triagem_diana_pendente($id_empresa);

        if (empty($itens)) {
            $this->debug('✗ Nenhum item encontrado para análise');
            return;
        }

        $this->debug('✓ Itens encontrados: ' . count($itens));

        // Processar cada item
        foreach ($itens as $item) {
            $this->debug("Processando: {$item->part_number} (Empresa: {$item->id_empresa}, Estabelecimento: {$item->estabelecimento})");

            try {
                // Chamar API DIANA
                $resposta_api = $this->diana_api->analisar_triagem(
                    $item->funcao,
                    $item->aplicacao,
                    $item->material_constitutivo,
                    $item->descricao
                );

                if ($resposta_api === false) {
                    $this->debug('✗ Falha na chamada da API');
                    continue;
                }

                $this->debug('✓ API respondeu com sucesso');
                $this->debug('Resposta: ' . json_encode($resposta_api, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

                $resultado = $this->diana_api->processar_resposta($resposta_api);
                $this->debug('Resultado processado: ' . json_encode($resultado, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

                // Atualizar item com resultado
                $sucesso = $this->item_model->processar_resultado_diana($item, $resultado);

                if ($sucesso) {
                    $this->debug('✓ Item atualizado com sucesso');
                } else {
                    $this->debug('✗ Falha ao atualizar item');
                }
            } catch (Exception $e) {
                $this->debug('✗ Erro: ' . $e->getMessage());
            }
        }
    }
}
