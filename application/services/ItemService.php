<?php

class ItemService {
    private $CI;

    private $prioridadesMap = null;
    private $prioridadeNormalId = null;

    public function __construct() 
    {
        $this->CI = &get_instance();

        $this->CI->load->model([
            'item_model',
            'cad_item_model',
            'foto_model',
            'usuario_model',
            'cest_model',
            'empresa_prioridades_model',
            'empresa_model',
            'owner_model'
        ]);

        $this->CI->load->helper('formatador_helper');
        $this->CI->load->library("Item/Status");
        $this->CI->load->library('Item/ItemDirect');

        // Carregar o serviço de importação Comex
        require_once APPPATH . 'services/ItemImportService.php';
        $this->importComexService = new ItemImportService;
    }

    /**
     * Processa um item da planilha, validando e inserindo/atualizando no banco de dados.
     *
     * @param array $itemData Dados do item da planilha.
     * @param object $empresa Objeto da empresa contendo informações como ID, CNPJ, etc.
     * @param array $saldo Informações de saldo da franquia.
     *                      Esperado: ['habilitar_uso_franquia' => bool, 'franquia_disponivel' => int]
     * @param int &$itens_processados Referência para o contador de itens processados (será incrementado por este método).
     * @param array $idx Mapeamento de colunas da planilha (ex: ['part_number' => 0, 'descricao' => 1, ...]).
     * @return array Logs do processamento do item, contendo chaves como 'inseridos', 'atualizados', 'nao_atualizados', 'com_erro', 'fotos_atualizadas'.
     */
    public function processItem($itemData, $empresa, $saldo, &$itens_processados, $idx)
    {
        $logs = [
            'inseridos' => [],
            'atualizados' => [],
            'nao_atualizados' => [],
            'com_erro' => [],
            'fotos_atualizadas' => []
        ];

        $id_empresa = $empresa->id_empresa;
        $can_formatar_texto = company_can("formatar_texto", explode('|', $empresa->funcoes_adicionais));

        $part_number = $itemData['part_number'];
        $cnpj = $itemData['cnpj'];
        
        $estabelecimento = empty($itemData['estabelecimento']) ?
            $empresa->estabelecimento_default :
            $itemData['estabelecimento'];

        $linha = isset($itemData['_linha']) ? $itemData['_linha'] : null;

        // Validações iniciais
        if (empty($estabelecimento)) {
            $logs['com_erro'][] = $this->addErrorLog(
                $part_number, 
                $estabelecimento, 
                "Part number [<b>{$part_number}</b>] - Estabelecimento não informado.",
                $linha,
                isset($idx['estabelecimento']) ? $this->num2alpha($idx['estabelecimento']) : null
            );
            return $logs;
        }
        if (empty($cnpj)) {
            $logs['com_erro'][] = "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] - CNPJ não informado.";
            return $logs;
        }
        if ($empresa->cnpj != $cnpj) {
            $logs['com_erro'][] = $this->addErrorLog(
                $part_number, 
                $estabelecimento, 
                "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] - O CNPJ [{$cnpj}] não confere com a empresa [{$empresa->cnpj}] ({$empresa->razao_social})",
                $linha,
                isset($idx['cnpj']) ? $this->num2alpha($idx['cnpj']) : null
            );
            return $logs;
        }

        $check_estabelecimento = ($empresa->multi_estabelecimentos == 1 ? $estabelecimento : null);
        $item = $this->CI->item_model->get_entry($part_number, $id_empresa, $check_estabelecimento);

        if ($item && ($estabelecimento != trim($item->estabelecimento))) {
            $logs['com_erro'][] = "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] - Item com estabelecimento divergente: <b>{$item->estabelecimento}</b>";
            return $logs;
        }

        $dbdata = $this->prepareItemData(
            $itemData,
            $idx,
            $can_formatar_texto,
            $id_empresa,
            $estabelecimento
        );
        
        // Validar todos os dados do item
        $validationLogs = $this->validateItemData(
            $dbdata,
            $idx,
            $part_number,
            $estabelecimento,
            $itemData,
            $id_empresa
        );

        $logs = array_merge($logs, $validationLogs);

        $this->importComexService->processImportStatus(
            $itemData,
            $idx,
            $part_number,
            $estabelecimento,
            $id_empresa
        );

        if (!empty($logs['com_erro'])) {
            return $logs;
        }

        // Processamento de fotos
        $list_fotos = preg_grep('/^foto_(\d+)$/', array_keys($idx));
        if (count($list_fotos)) {
            $fotoLogs = $this->processPhotos($dbdata, $list_fotos, $itemData, $idx);
            $logs = array_merge($logs, $fotoLogs);
            if (!empty($fotoLogs['com_erro'])) {
                return $logs;
            }
        }

        // Inserção ou atualização
        if ($item) {
            $force = isset($idx['forcar_atualizacao']) ?
                mb_strtolower($itemData['forcar_atualizacao']) :
                'nao';
            
            // Se não tiver saldo na franquia, não atualiza o item
            if (!$this->checkFranquia($saldo, $itens_processados)) {
                $logs['com_erro'][] = "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] - Quantidade da franquia excedida";
                return $logs;
            }

            if ($force == 'sim' && $this->checkFranquia($saldo, $itens_processados)) {
                $this->CI->db->update('item', $dbdata, ['part_number' => $item->part_number, 'id_empresa' => $id_empresa, 'estabelecimento' => $estabelecimento]);

                $itens_processados++;

                generate_item_log(
                    'atualizacao',
                    [
                        'part_number' => $item->part_number, 
                        'estabelecimento' => $item->estabelecimento, 
                        'id_empresa' => $item->id_empresa
                    ],
                    $item
                );

                $logs['atualizados'][] = "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] atualizado";
                $tipo = ['retornar_erro' => false, 'atualizar' => true];
                
                if ($this->CI->itemdirect->isDirectItem((array)$item, $cnpj, $tipo)) {
                    $this->CI->status->set_status("homologar");
                    $this->CI->status->update_item($item->part_number, $item->estabelecimento);
                }
            } else {
                $logs['nao_atualizados'][] = "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] não foi atualizado";
            }
        } else {
            // Se não tiver saldo na franquia, não insere o item
            if (!$this->checkFranquia($saldo, $itens_processados)) {
                $logs['com_erro'][] = "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] - Quantidade da franquia excedida";
                return $logs;
            }

            if ($this->checkFranquia($saldo, $itens_processados)) {
                $this->CI->db->insert('item', $dbdata);
                $itens_processados++;
                $tipo = ['retornar_erro' => false, 'atualizar' => false];
                if ($this->CI->itemdirect->isDirectItem($dbdata, $cnpj, $tipo)) {
                    $this->CI->status->set_status("homologar");
                    $this->CI->status->update_item($dbdata['part_number'], $dbdata['estabelecimento']);
                }
                generate_item_log(
                    'criacao',
                    ['part_number' => $dbdata['part_number'],
                    'estabelecimento' => $dbdata['estabelecimento'],
                    'id_empresa' => $dbdata['id_empresa']]
                );
                
                $logs['inseridos'][] = "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] foi inserido";
            }
        }

        return $logs;
    }

    /**
     * Prepara os dados do item para inserção ou atualização no banco de dados.
     *
     * @param array $itemData Dados do item provenientes da planilha.
     * @param array $idx Mapeamento de colunas da planilha.
     * @param bool $can_formatar_texto Indica se a formatação de texto está habilitada.
     * @param int $id_empresa ID da empresa.
     * @param string $estabelecimento Identificador do estabelecimento.
     * @return array Array com os dados do item formatados e prontos para o banco de dados.
     */
    private function prepareItemData($itemData, $idx, $can_formatar_texto, $id_empresa, $estabelecimento) 
    {
        $dbdata = [
            'part_number' => $itemData['part_number'],
            'estabelecimento' => $estabelecimento,
            'id_empresa' => $id_empresa,
            'status' => '1-sugerido_becomex',
            'data_envio' => date('Y-m-d H:i:s'),
        ];

        if (isset($idx['descricao'])) {
            $dbdata['descricao'] = formatar_texto($can_formatar_texto, $itemData['descricao']);
        }

        if (isset($idx['descricao_global'])) {
            $dbdata['descricao_global'] = formatar_texto($can_formatar_texto, $itemData['descricao_global']);
        }

        if (isset($idx['ncm_atual'])) {
            $dbdata['ncm'] = $itemData['ncm_atual'];
        }

        if (isset($idx['ncm_fornecedor'])) {
            $dbdata['ncm_fornecedor'] = $itemData['ncm_fornecedor'];
        }

        if (isset($idx['peso'])) {
            $dbdata['peso'] = $itemData['peso'];
        }

        if (isset($idx['prioridade'])) {
            $dbdata['prioridade'] = $itemData['prioridade'];
        }

        if (isset($idx['cod_cest'])) {
            $dbdata['cod_cest'] = $itemData['cod_cest'];
        }

        if (isset($idx['tag']) && !empty($itemData['tag'])) {
            $dbdata['tag'] = $itemData['tag'];
        }

        $dbdata['gestao_mensal'] = 1;
        $gestao_mensal = isset($idx['gestao_mensal']) ? trim($itemData['gestao_mensal']) : '';
        if (!empty($gestao_mensal)) {
            if (in_array($gestao_mensal, array("SIM", "sim", "Sim", "S", "1"))) {
                $dbdata['gestao_mensal'] = 1;
            }elseif(in_array($gestao_mensal, array("NÃO", "não", "Não","NAO", "N", "0"))){
                $dbdata['gestao_mensal'] = 0;
            }
        }

        if (isset($idx['descricao_proposta_completa'])) {
            $dbdata['descricao_proposta_completa'] = formatar_texto($can_formatar_texto, $itemData['descricao_proposta_completa']);
        }

        if (isset($idx['aplicacao'])) {
            if ($aplicacao = trim($itemData['aplicacao'])) {
                $dbdata['aplicacao'] = formatar_texto($can_formatar_texto, $aplicacao);
            }
        }

        if (isset($idx['marca'])) {
            if ($marca = trim($itemData['marca'])) {
                $dbdata['marca'] = formatar_texto($can_formatar_texto, $marca);
            }
        }

        if (isset($idx['material_constitutivo'])) {
            if ($material_constitutivo = trim($itemData['material_constitutivo'])) {
                $dbdata['material_constitutivo'] = formatar_texto($can_formatar_texto, $material_constitutivo);
            }
        }

        if (isset($idx['evento'])) {
            if ($evento = trim($itemData['evento'])) {
                $dbdata['evento'] = formatar_texto($can_formatar_texto, $evento);
            }
        }

        if (isset($idx['memoria_classificacao'])) {
            if ($memoria_classificacao = trim($itemData['memoria_classificacao'])) {
                $dbdata['memoria_classificacao'] = $memoria_classificacao;
            }
        }

        if (isset($idx['maquina'])) {
            $dbdata['maquina'] = $itemData['maquina'];
        }

        if (isset($idx['origem'])) {
            $dbdata['origem'] = $itemData['origem'];
        }

        if (isset($idx['pn_secundario_ipn'])) {
            $dbdata['pn_secundario_ipn'] = $itemData['pn_secundario_ipn'];
        }

        if (isset($idx['pn_primario_mpn'])) {
            $dbdata['pn_primario_mpn'] = $itemData['pn_primario_mpn'];
        }

        if (isset($idx['observacoes'])) {
            $dbdata['observacoes'] = $itemData['observacoes'];
        }

        if (isset($idx['lista_cliente'])) {
            $dbdata['lista_cliente'] = $itemData['lista_cliente'];
        }

        if (isset($idx['inf_adicionais'])) {
            $dbdata['inf_adicionais'] = formatar_texto($can_formatar_texto, $itemData['inf_adicionais']);
        }

        if (isset($idx['funcao'])) {
            if ($funcao = trim($itemData['funcao'])) {
                $dbdata['funcao'] = formatar_texto($can_formatar_texto, $funcao);
            }
        }

        return $dbdata;
    }

    /**
     * Valida os dados do item e adiciona erros ao log
     *
     * @param array &$dbdata Dados do item para inserção/atualização
     * @param array $idx Índices dos campos
     * @param string $part_number Número da peça
     * @param string $estabelecimento Estabelecimento
     * @param array $itemData Dados originais do item
     * @param int $id_empresa ID da empresa
     * @return array Logs de erros encontrados
     */
    private function validateItemData(&$dbdata, $idx, $part_number, $estabelecimento, $itemData, $id_empresa) 
    {
        $logs = ['com_erro' => []];
        
        // Validar usuários responsáveis
        $userLogs = $this->validateResponsibleUsers($itemData, $idx, $part_number, $estabelecimento, $dbdata);
        $logs['com_erro'] = array_merge($logs['com_erro'], $userLogs['com_erro']);
        
        // Validar CEST
        $cestLogs = $this->validateCest($itemData, $idx, $part_number, $estabelecimento, $dbdata);
        $logs['com_erro'] = array_merge($logs['com_erro'], $cestLogs['com_erro']);
        
        // Validar Owner
        $ownerLogs = $this->validateOwner($itemData, $idx, $part_number, $estabelecimento, $dbdata);
        $logs['com_erro'] = array_merge($logs['com_erro'], $ownerLogs['com_erro']);
        
        // Validar Prioridade
        $prioridadeLogs = $this->validatePrioridade(
            $itemData,
            $idx,
            $part_number,
            $estabelecimento,
            $dbdata,
            $id_empresa
        );

        $logs['com_erro'] = array_merge($logs['com_erro'], $prioridadeLogs['com_erro']);
        
        // Adicione outras validações específicas aqui conforme necessário
        
        return $logs;
    }

    /**
     * Processa as fotos de um item, replicando a lógica original do controller
     *
     * @param array $dbdata Dados do item preparados para inserção/atualização
     * @param array $list_fotos Lista de chaves das colunas de fotos (ex.: foto_1, foto_2)
     * @param array $itemData Dados originais da linha da planilha
     * @param array $idx Mapa de índices das colunas da planilha
     * @return array Logs de erros e atualizações de fotos
     */
    private function processPhotos($dbdata, $list_fotos, $itemData, $idx)
    {
        $logs = ['com_erro' => [], 'fotos_atualizadas' => []];
        $part_number = $dbdata['part_number'];
        $id_empresa = $dbdata['id_empresa'];
        $estabelecimento = $dbdata['estabelecimento'];

        // Passo 1: Verificar fotos duplicadas na mesma linha
        $fotos = [];
        foreach ($list_fotos as $foto_key) {
            $arquivo_slot = trim($itemData[$foto_key]);
            if (!empty($arquivo_slot)) {
                if (in_array($arquivo_slot, $fotos)) {
                    $logs['com_erro'][] = $this->addErrorLog(
                        $part_number,
                        $estabelecimento,
                        "Existe mais de uma foto com o mesmo nome de arquivo [<b>{$arquivo_slot}</b>] na planilha para o item.",
                        $itemData['linha'],
                        $idx[$foto_key]
                    );
                    return $logs; // Interrompe o processamento se houver duplicatas
                }
                $fotos[] = $arquivo_slot;
            }
        }

        // Passo 2: Verificar se já existe foto com o mesmo nome em ordem diferente
        $erro_fotos = false;
        foreach ($list_fotos as $foto_key) {
            $arquivo = trim($itemData[$foto_key]);
            if (!empty($arquivo)) {
                $arquivo = strtolower(basename($arquivo, '.jpg')) . '.jpg';
                $ordem = preg_replace('/^foto_(\d+)$/', '$1', $foto_key);

                $this->CI->db->where('part_number', $part_number);
                $this->CI->db->where('id_empresa', $id_empresa);
                $this->CI->db->where('estabelecimento', $estabelecimento);
                $this->CI->db->where('arquivo', $arquivo);
                $this->CI->db->where("ordem != '{$ordem}'", null, false);
                $query_check_foto = $this->CI->db->get('foto');

                if ($query_check_foto->num_rows() > 0) {
                    $logs['com_erro'][] = $this->addErrorLog(
                        $part_number,
                        $estabelecimento,
                        "Já existe uma foto de nome '{$arquivo}' cadastrada para o item em outra ordem.",
                        $itemData['linha'],
                        $idx[$foto_key]
                    );
                    $erro_fotos = true;
                }
            }
        }

        if ($erro_fotos) {
            return $logs; // Interrompe se houver erros
        }

        // Passo 3: Inserção/Atualização de fotos
        $update_foto = false;
        foreach ($list_fotos as $foto_key) {
            $arquivo = trim($itemData[$foto_key]);
            if (!empty($arquivo)) {
                $ordem = preg_replace('/^foto_(\d+)$/', '$1', $foto_key);
                $arquivo = strtolower(basename($arquivo, '.jpg')) . '.jpg';

                $dbdata2 = [
                    'arquivo' => $arquivo,
                    'id_empresa' => $id_empresa,
                    'part_number' => $part_number,
                    'estabelecimento' => $estabelecimento,
                    'ordem' => $ordem,
                ];

                // Verifica foto existente para a mesma ordem
                $old_arquivo = $this->CI->foto_model->get_arquivo_by_pn($part_number, $id_empresa, $ordem, $estabelecimento);

                // Remove foto existente, se houver
                $this->CI->db->where('ordem', $ordem);
                $this->CI->db->where('part_number', $part_number);
                $this->CI->db->where('id_empresa', $id_empresa);
                $this->CI->db->where('estabelecimento', $estabelecimento);
                if ($this->CI->db->delete('foto')) {
                    if ($dbdata2['arquivo'] != $old_arquivo && !empty($old_arquivo)) {
                        $data_log = [
                            'part_number' => $dbdata2['part_number'],
                            'id_empresa' => $dbdata2['id_empresa'],
                            'estabelecimento' => $estabelecimento,
                            'ordem' => $dbdata2['ordem'],
                            'arquivo' => $old_arquivo,
                            'id_usuario' => sess_user_id(),
                            'criado_em' => date('Y-m-d H:i:s'),
                            'tipo' => 'delete'
                        ];
                        $this->CI->foto_model->save_log($data_log);
                    }
                }

                // Insere a nova foto
                if ($this->CI->db->insert('foto', $dbdata2)) {
                    if ($dbdata2['arquivo'] != $old_arquivo) {
                        $update_foto = true;
                        $data_log = [
                            'part_number' => $dbdata2['part_number'],
                            'id_empresa' => $dbdata2['id_empresa'],
                            'estabelecimento' => $estabelecimento,
                            'ordem' => $dbdata2['ordem'],
                            'arquivo' => $dbdata2['arquivo'],
                            'id_usuario' => sess_user_id(),
                            'criado_em' => date('Y-m-d H:i:s'),
                            'tipo' => 'insert'
                        ];
                        $this->CI->foto_model->save_log($data_log);
                    }
                }
            }
        }

        if ($update_foto) {
            $logs['fotos_atualizadas'][] = "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] atualizado";
        }

        return $logs;
    }

    /**
     * Verifica se o limite da franquia foi excedido.
     *
     * @param array $saldo Array contendo informações da franquia.
     *                      Esperado: ['habilitar_uso_franquia' => bool, 'franquia_disponivel' => int]
     * @param int $itens_processados Número de itens já processados.
     * @return bool Retorna false se o limite da franquia foi atingido, true caso contrário.
     */
    private function checkFranquia($saldo, $itens_processados)
    {
        if (isset($saldo['habilitar_uso_franquia']) &&
            $saldo['habilitar_uso_franquia'] == 1 &&
            ($saldo['franquia_disponivel'] - $itens_processados) <= 0) {

            return false;
        }

        return true;
    }
    

    /**
     * Valida e prepara os dados de usuários responsáveis
     *
     * @param array $itemData Dados do item
     * @param array $idx Índices dos campos
     * @param string $part_number Número da peça
     * @param string $estabelecimento Estabelecimento
     * @param array &$dbdata Array de dados para inserção/atualização
     * @return array Logs de erros encontrados
     */
    private function validateResponsibleUsers(
        $itemData,
        $idx,
        $part_number,
        $estabelecimento,
        &$dbdata
    )
    {
        $logs = ['com_erro' => []];
        
        // Validar responsável fiscal
        if (isset($idx['id_resp_fiscal'])) {
            if (empty($itemData['id_resp_fiscal'])) {
                $logs['com_erro'][] = $this->addErrorLog(
                    $part_number,
                    $estabelecimento,
                    "Usuário responsável fiscal não informado.",
                    $itemData['linha'],
                    $idx['id_resp_fiscal']
                );
            } else {
                $id_resp_fiscal = $this->CI->usuario_model->get_user_id_by_email($itemData['id_resp_fiscal']);
                
                if (empty($id_resp_fiscal)) {
                    $logs['com_erro'][] = $this->addErrorLog(
                        $part_number,
                        $estabelecimento,
                        "Usuário responsável fiscal informado não existe.",
                        $itemData['linha'] ?? $itemData['_linha'],
                        $idx['id_resp_fiscal']
                    );
                } else {
                    $dbdata['id_resp_fiscal'] = $id_resp_fiscal;
                }
            }
        } else {
            $logs['com_erro'][] = $this->addErrorLog(
                $part_number,
                $estabelecimento,
                "Usuário responsável fiscal não informado.",
                $itemData['linha'],
                $idx['id_resp_fiscal']
            );
        }
        
        // Validar responsável engenharia
        if (isset($idx['id_resp_engenharia'])) {
            if (empty($itemData['id_resp_engenharia'])) {
                $logs['com_erro'][] = $this->addErrorLog(
                    $part_number,
                    $estabelecimento,
                    "Usuário responsável engenharia não informado.",
                    $itemData['linha'] ?? $itemData['_linha'],
                    $idx['id_resp_engenharia']
                );
            } else {
                $id_resp_engenharia = $this->CI->usuario_model->get_user_id_by_email($itemData['id_resp_engenharia']);
                
                if (empty($id_resp_engenharia)) {
                    $logs['com_erro'][] = $this->addErrorLog(
                        $part_number,
                        $estabelecimento,
                        "Usuário responsável engenharia informado não existe.",
                        $itemData['linha'],
                        $idx['id_resp_engenharia']
                    );
                } else {
                    $dbdata['id_resp_engenharia'] = $id_resp_engenharia;
                }
            }
        } else {
            $logs['com_erro'][] = $this->addErrorLog(
                $part_number,
                $estabelecimento,
                "Usuário responsável engenharia não informado.",
                $itemData['linha'],
                $idx['id_resp_engenharia']
            );
        }
        
        return $logs;
    }

    /**
     * Valida e prepara os dados de CEST
     *
     * @param array $itemData Dados do item
     * @param array $idx Índices dos campos
     * @param string $part_number Número da peça
     * @param string $estabelecimento Estabelecimento
     * @param array &$dbdata Array de dados para inserção/atualização
     * @return array Logs de erros encontrados
     */
    private function validateCest($itemData, $idx, $part_number, $estabelecimento, &$dbdata)
    {
        $logs = ['com_erro' => []];
        
        if (isset($idx['cod_cest']) && !empty($itemData['cod_cest'])) {
            $cod_cest = trim($itemData['cod_cest']);
            
            if (!$this->CI->cest_model->get_entry($cod_cest)) {
                $logs['com_erro'][] = $this->addErrorLog(
                    $part_number,
                    $estabelecimento,
                    "Código CEST [<b>{$cod_cest}</b>] não encontrado!",
                    $itemData['linha'],
                    $idx['cod_cest']
                );
            } else {
                $dbdata['cod_cest'] = $cod_cest;
            }
        }
        
        return $logs;
    }

    /**
     * Valida e prepara os dados de Owner
     *
     * @param array $itemData Dados do item
     * @param array $idx Índices dos campos
     * @param string $part_number Número da peça
     * @param string $estabelecimento Estabelecimento
     * @param array &$dbdata Array de dados para inserção/atualização
     * @return array Logs de erros encontrados
     */
    private function validateOwner($itemData, $idx, $part_number, $estabelecimento, &$dbdata)
    {
        $logs = ['com_erro' => []];
        
        if (isset($idx['cod_owner']) && !empty($itemData['cod_owner'])) {
            $codigo_owner = trim($itemData['cod_owner']);
            $codigo_owner_dbdata = $this->CI->owner_model->get_owner($codigo_owner);
            
            if (!empty($codigo_owner_dbdata->codigo)) {
                $dbdata['cod_owner'] = $codigo_owner_dbdata->codigo;
            } else {
                $logs['com_erro'][] = $this->addErrorLog(
                    $part_number,
                    $estabelecimento,
                    "Owner não encontrado!",
                    $itemData['linha'],
                    $idx['cod_owner']
                );
            }
        }
        
        return $logs;
    }

    /**
     * Inicializa o mapa de prioridades para uma empresa
     *
     * @param int $id_empresa ID da empresa
     */
    private function initPrioridadesMap($id_empresa)
    {
        if ($this->prioridadesMap === null) {
            $this->prioridadesMap = [];
            $empresa_prioridades = $this->CI->empresa_prioridades_model->get_entry($id_empresa);
            
            foreach ($empresa_prioridades as $p) {
                $nome_normalizado = strtoupper(remove_acentos($p->nome));
                $this->prioridadesMap[$nome_normalizado] = $p->id_prioridade;
                
                // Armazenar o ID da prioridade "NORMAL" para uso padrão
                if ($nome_normalizado === 'NORMAL') {
                    $this->prioridadeNormalId = $p->id_prioridade;
                }
            }
            
            // Adicionar mapeamento especial para "SUPER CRITICO" -> "SUPERCRITICO"
            if (isset($this->prioridadesMap['SUPERCRITICO'])) {
                $this->prioridadesMap['SUPER CRITICO'] = $this->prioridadesMap['SUPERCRITICO'];
            }

        }
    }

    /**
     * Valida e prepara os dados de prioridade
     *
     * @param array $itemData Dados do item
     * @param array $idx Índices dos campos
     * @param string $part_number Número da peça
     * @param string $estabelecimento Estabelecimento
     * @param array &$dbdata Array de dados para inserção/atualização
     * @param int $id_empresa ID da empresa
     * @return array Logs de erros encontrados
     */
    private function validatePrioridade($itemData, $idx, $part_number, $estabelecimento, &$dbdata, $id_empresa)
    {
        $logs = ['com_erro' => []];
        
        // Inicializar o mapa de prioridades (será carregado apenas uma vez)
        $this->initPrioridadesMap($id_empresa);
        
        if (isset($idx['prioridade'])) {
            $prioridade = !empty($itemData['prioridade']) ? trim($itemData['prioridade']) : '';
            
            if (!empty($prioridade)) {
                $prioridade_normalizada = strtoupper(remove_acentos($prioridade));
                
                if (isset($this->prioridadesMap[$prioridade_normalizada])) {
                    $dbdata['id_prioridade'] = $this->prioridadesMap[$prioridade_normalizada];
                } else {
                    $logs['com_erro'][] = $this->addErrorLog(
                        $part_number,
                        $estabelecimento,
                        "Definição de prioridade: [<b>{$prioridade}</b>], Inválida!",
                        $itemData['linha'],
                        $idx['prioridade']
                    );
                }
            } else {
                // Se prioridade não foi informada, usar a prioridade "NORMAL"
                if ($this->prioridadeNormalId !== null) {
                    $dbdata['id_prioridade'] = $this->prioridadeNormalId;
                } else {
                    $logs['com_erro'][] = $this->addErrorLog(
                        $part_number,
                        $estabelecimento,
                        "Prioridade padrão 'NORMAL' não encontrada!",
                        $itemData['linha'],
                        $idx['prioridade']
                    );
                }
            }
        }
        
        return $logs;
    }

    /**
     * Adiciona um erro ao log com informações detalhadas
     *
     * @param string $part_number Número da peça
     * @param string $estabelecimento Estabelecimento
     * @param string $mensagem Mensagem de erro
     * @param int $linha Número da linha (opcional)
     * @param string|int $coluna Nome ou índice da coluna (opcional)
     * @return array Entrada de log formatada
     */
    private function addErrorLog($part_number, $estabelecimento, $mensagem, $linha = null, $coluna = null)
    {
        // Garantir que part_number e estabelecimento nunca sejam vazios no log
        $part_number = !empty($part_number) ? $part_number : 'N/A';
        $estabelecimento = !empty($estabelecimento) ? $estabelecimento : 'N/A';
        
        // Formato detalhado para o relatório
        $log_entry = [
            'part_number' => $part_number,
            'estabelecimento' => $estabelecimento,
            'mensagem' => $mensagem,
            'linha' => $linha,
            'coluna' => $coluna
        ];
        
        return $log_entry;
    }

    /**
     * Converte número de coluna para letra (ex: 0 -> A, 1 -> B)
     * 
     * @param int $n Número da coluna
     * @return string Letra da coluna
     */
    private function num2alpha($n) 
    {
        $r = '';
        for ($i = 1; $n >= 0 && $i < 10; $i++) {
            $r = chr(0x41 + ($n % 26)) . $r;
            $n = floor($n / 26) - 1;
        }
        return $r;
    }
}
