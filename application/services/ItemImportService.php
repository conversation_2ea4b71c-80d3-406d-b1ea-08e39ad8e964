<?php

class ItemImportService {
    private $CI;

    public function __construct()
    {
        $this->CI = &get_instance();

        $this->CI->load->model([
            'cad_item_model',
            'empresa_model'
        ]);
    }

    /**
     * Processa o status de importação de um item
     *
     * @param array $itemData Dados do item
     * @param array $idx Índices dos campos
     * @param string $part_number Número da peça
     * @param string $estabelecimento Estabelecimento
     * @param int $id_empresa ID da empresa
     * @return bool True se o item foi processado, False caso contrário
     */
    public function processImportStatus($itemData, $idx, $part_number, $estabelecimento, $id_empresa)
    {
        // Verificar permissão de importação da empresa
        $item_importado_default = $this->CI->empresa_model->check_imported_item_permission($id_empresa);
        
        // Caso 1: Campo importado não está definido, mas empresa tem permissão padrão
        if (!isset($idx['importado']) && $item_importado_default) {
            $this->CI->cad_item_model->define_item_importado($part_number, $estabelecimento, $id_empresa);
            return true;
        }
        
        // Caso 2: Campo importado está definido e valor é "SIM"
        if (isset($idx['importado']) && isset($itemData['importado']) &&
            strtoupper($itemData['importado']) == 'SIM') {
            $this->CI->cad_item_model->define_item_importado($part_number, $estabelecimento, $id_empresa);
            return true;
        }
        
        // Caso 3: Campo importado está definido e valor é "NAO" ou "NÃO"
        if (isset($idx['importado']) && isset($itemData['importado']) &&
            (strtoupper($itemData['importado']) == 'NAO' || strtoupper($itemData['importado']) == 'NÃO')) {
            $this->CI->cad_item_model->remove_item_importado($part_number, $id_empresa, $estabelecimento);
            return true;
        }
        
        return false;
    }
}
