<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class LogService {
    private $CI;

    public function __construct() {
        $this->CI = &get_instance();
    }

    /**
     * Formata os logs para exibição
     *
     * @param array $inseridos Itens inseridos
     * @param array $atualizados Itens atualizados
     * @param array $nao_atualizados Itens não atualizados
     * @param array $com_erro Itens com erro
     * @param array $fotos_atualizadas Fotos atualizadas
     * @param string $filename Nome do arquivo processado
     * @return array Mensagem formatada e tipo
     */
    public function formatLog(
        $inseridos,
        $atualizados,
        $nao_atualizados,
        $com_erro,
        $fotos_atualizadas,
        $filename
    )
    {
        $message = '';
        $type = 'success';
        
        // Contadores - precisamos calcular o total de erros corretamente
        $total_inseridos = count($inseridos);
        $total_atualizados = count($atualizados);
        $total_nao_atualizados = count($nao_atualizados);
        
        // Calcular total de erros, incluindo os erros de planilha
        $total_com_erro = 0;
        foreach ($com_erro as $key => $value) {
            if ($key === 'planilha_errors') {
                $total_com_erro += count($value);
            } else {
                $total_com_erro++;
            }
        }
        
        $total_fotos = count($fotos_atualizadas);
        
        // Cabeçalho
        $message .= "<p><strong>Arquivo processado:</strong> {$filename}</p>";
        
        // Resumo
        $message .= "<h4>Resumo do processamento:</h4>";
        $message .= "<ul>";
        
        if ($total_inseridos > 0) {
            $message .= "<li><span class='text-success'>{$total_inseridos} item(ns) inserido(s)</span></li>";
        }
        
        if ($total_atualizados > 0) {
            $message .= "<li><span class='text-info'>{$total_atualizados} item(ns) atualizado(s)</span></li>";
        }
        
        if ($total_nao_atualizados > 0) {
            $message .= "<li><span class='text-warning'>{$total_nao_atualizados} item(ns) não atualizado(s)</span></li>";
        }
        
        if ($total_com_erro > 0) {
            $message .= "<li><span class='text-danger'>{$total_com_erro} item(ns) com erro</span></li>";
            $type = 'warning'; // Mudar o tipo para warning se houver erros
        }
        
        if ($total_fotos > 0) {
            $message .= "<li>{$total_fotos} foto(s) atualizada(s)</li>";
        }
        
        $message .= "</ul>";
        
        // Detalhes (limitados para não sobrecarregar a interface)
        $max_items = 5; // Número máximo de itens a mostrar em cada categoria
        
        // Itens inseridos
        if ($total_inseridos > 0) {
            $message .= "<h4>Itens inseridos:</h4>";
            $message .= "<ul class='text-success'>";
            
            $count = 0;
            foreach ($inseridos as $item) {
                if ($count < $max_items) {
                    $message .= "<li>{$item}</li>";
                    $count++;
                } else {
                    $remaining = $total_inseridos - $max_items;
                    if ($remaining > 0) {
                        $message .= "<li>... e mais {$remaining} item(ns)</li>";
                    }
                    break;
                }
            }
            
            $message .= "</ul>";
        }
        
        // Itens atualizados
        if ($total_atualizados > 0) {
            $message .= "<h4>Itens atualizados:</h4>";
            $message .= "<ul class='text-info'>";
            
            $count = 0;
            foreach ($atualizados as $item) {
                if ($count < $max_items) {
                    $message .= "<li>{$item}</li>";
                    $count++;
                } else {
                    $remaining = $total_atualizados - $max_items;
                    if ($remaining > 0) {
                        $message .= "<li>... e mais {$remaining} item(ns)</li>";
                    }
                    break;
                }
            }
            
            $message .= "</ul>";
        }
        
        // Itens com erro (mostrar todos, pois são importantes)
        if ($total_com_erro > 0) {
            $message .= "<h4>Itens com erro:</h4>";
            $message .= "<ul class='text-danger'>";
            
            $count = 0;
            $max_error_count = $max_items;
            
            // Primeiro processar os erros de planilha
            if (isset($com_erro['planilha_errors'])) {
                foreach ($com_erro['planilha_errors'] as $error) {
                    if ($count < $max_error_count) {
                        if (is_array($error)) {
                            if (isset($error['mensagem'])) {
                                $erro_msg = $error['mensagem'];
                                if (isset($error['linha'])) {
                                    $erro_msg .= " (Linha {$error['linha']})";
                                }
                                if (isset($error['coluna']) && $error['coluna'] !== 'N/A') {
                                    $erro_msg .= " (Coluna {$error['coluna']})";
                                }
                                $message .= "<li>{$erro_msg}</li>";
                            } else {
                                $message .= "<li>Erro não especificado</li>";
                            }
                        } else {
                            $message .= "<li>{$error}</li>";
                        }
                        $count++;
                    } else {
                        break;
                    }
                }
            }
            
            // Depois processar os outros erros
            foreach ($com_erro as $key => $error) {
                // Pular o array planilha_errors que já foi processado
                if ($key === 'planilha_errors') {
                    continue;
                }
                
                if ($count < $max_error_count) {
                    if (is_array($error)) {
                        // Formato detalhado
                        if (isset($error['mensagem'])) {
                            $erro_msg = $error['mensagem'];
                            if (isset($error['linha']) && $error['linha'] !== 'N/A') {
                                $erro_msg .= " (Linha {$error['linha']})";
                            }
                            if (isset($error['coluna']) && $error['coluna'] !== 'N/A') {
                                $erro_msg .= " (Coluna {$error['coluna']})";
                            }
                            $message .= "<li>{$erro_msg}</li>";
                        } else {
                            $message .= "<li>Erro não especificado</li>";
                        }
                    } else {
                        // Formato simples (string)
                        $message .= "<li>{$error}</li>";
                    }
                    
                    $count++;
                } else {
                    break;
                }
            }
            
            // Adicionar mensagem de "mais erros" se necessário
            if ($count < $total_com_erro) {
                $remaining = $total_com_erro - $count;
                $message .= "<li>... e mais {$remaining} erro(s). Veja o relatório completo para detalhes.</li>";
            }
            
            $message .= "</ul>";
        }
        
        return [
            'message' => $message,
            'type' => $type
        ];
    }
}
