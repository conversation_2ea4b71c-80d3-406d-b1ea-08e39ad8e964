<?php
class UploadService {
    private $CI;

    public function __construct() {
        $this->CI = &get_instance();
        $this->CI->load->library('upload');
    }

    public function doUpload($config) {
        $this->CI->upload->initialize($config);
        if (!$this->CI->upload->do_upload('arquivo')) {
            return ['success' => false, 'errors' => $this->CI->upload->display_errors('<p>', '</p>')];
        }
        return ['success' => true, 'data' => $this->CI->upload->data()];
    }
}
