<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Add_Column_Emails_Dest_Revisao_Pucomex extends CI_Migration {

    public function up()
    {   
        if ($this->db->table_exists('empresa')) {
            if (!$this->db->field_exists('destinatarios_revisao_pucomex', 'empresa')) {
                $fields = array(
                    'destinatarios_revisao_pucomex' => array(
                        'type' => 'TEXT', 
                        'null' => TRUE
                    ),
                );
                $this->dbforge->add_column('empresa', $fields);
            }
        }
        
    }
 
    public function down()
    {
        if ($this->db->table_exists('empresa')) {
            if ($this->db->field_exists('destinatarios_revisao_pucomex', 'empresa')) {
                $this->dbforge->drop_column('empresa', 'destinatarios_revisao_pucomex');
            }
        }
    }
}
