<?php echo form_open('', array('class' => 'form-horizontal')) ?>

<div class="page-header">
    <h2>
        Editar prioridade
    </h2>
</div>

<div class="form-group">
    <label for="input-ordem" class="col-sm-2 control-label">Ordem</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" name="ordem" id="input-ordem" value="<?php echo set_value('ordem', $entry->ordem) ?>" placeholder="Ordem">
    </div>
</div>

<div class="form-group">
    <label for="input-nome" class="col-sm-2 control-label">Nome</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" name="nome" id="input-nome" value="<?php echo set_value('nome', $entry->nome) ?>" placeholder="Nome">
    </div>
</div>

<div class="form-group">
    <label for="input-horas" class="col-sm-2 control-label">Horas</label>
    <div class="col-sm-10">
        <input type="number" class="form-control" name="qdt_horas_uteis" id="input-horas" value="<?php echo set_value('horas', $entry->qdt_horas_uteis) ?>" placeholder="Horas">
    </div>
</div>


<div class="form-group">
    <label for="input-valor-padrao" class="col-sm-2 control-label">Valor do item padrão</label>
    <div class="col-sm-10">
        <input type="text" class="form-control valor-decimal-br" name="valor_padrao" id="input-valor-padrao" value="<?php echo set_value('valor-padrao', $entry->valor_padrao) ?>" placeholder="Valor Padrão">
    </div>
</div>

<div class="form-group">
    <label for="input-valor-quimico" class="col-sm-2 control-label">Valor do item químico</label>
    <div class="col-sm-10">
        <input type="text" class="form-control valor-decimal-br" name="valor_quimico" id="input-valor-quimico" value="<?php echo set_value('valor-quimico', $entry->valor_quimico) ?>" placeholder="Valor Químico">
    </div>
</div>
 


<div class="row" style="margin-bottom: 30px;">
    <hr />
    <div class="col-sm-5">
        <button type="submit" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
        <a href="<?php echo site_url("cadastros/prioridades") ?>" class="btn">Cancelar</a>
    </div>
 
</div>

</form>
<script type="text/javascript">
$(document).ready(function() {
    // Aplica a lógica a todos os inputs com a classe 'valor-decimal-br'
    // Usar delegação de eventos para cobrir inputs adicionados dinamicamente, se houver.
    $('body').on('keypress', '.valor-decimal-br', function(event) {
        var $this = $(this);
        var charCode = (event.which) ? event.which : event.keyCode;
        var value = $this.val();
        var cursorPosition = this.selectionStart;
        var selectionEnd = this.selectionEnd;
        var hasSelection = selectionEnd !== cursorPosition;

        // Permite: números (0-9)
        if (charCode >= 48 && charCode <= 57) {
            var commaIndex = value.indexOf(',');
            if (commaIndex !== -1 && cursorPosition > commaIndex && value.substring(commaIndex + 1).length >= 2 && !hasSelection) {
                // Se o cursor está após a vírgula e já existem 2 casas decimais,
                // não permite mais dígitos, a menos que esteja substituindo uma seleção.
                event.preventDefault();
            }
            return true; // Permite o dígito
        }

        // Permite: uma única VÍRGULA decimal
        if (charCode === 44) { // Vírgula (,)
            if (value.indexOf(',') === -1) { // Só permite se não houver outra vírgula
                return true;
            } else {
                event.preventDefault(); // Bloqueia vírgula extra
                return false;
            }
        }

        // Permite: teclas de controle como backspace, delete, tab, escape, enter, setas
        if (charCode === 8 || charCode === 46 || charCode === 9 || charCode === 27 || charCode === 13 ||
            (charCode >= 35 && charCode <= 40)) { // Home, End, Setas
            return true;
        }
        // Permite: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X (e equivalentes no Mac)
        if ((event.ctrlKey || event.metaKey) && 
            (charCode === 97 || charCode === 99 || charCode === 118 || charCode === 120 || // a, c, v, x
             charCode === 65 || charCode === 67 || charCode === 86 || charCode === 88 )) { // A, C, V, X
             return true;
        }

        // Bloqueia todas as outras teclas
        event.preventDefault();
        return false;
    });

    $('body').on('blur', '.valor-decimal-br', function() {
        var $this = $(this);
        var value = $this.val();

        if (value === "" || value === null) {
            $this.val("");  
            return;
        }
 
        var tempValue = "";
        var commaFound = false;
        for (var i = 0; i < value.length; i++) {
            var char = value[i];
            if (char >= '0' && char <= '9') {
                tempValue += char;
            } else if (char === ',' && !commaFound) {
                tempValue += char;
                commaFound = true;
            }
        }
        value = tempValue;
 
        var valueForParsing = value.replace(',', '.');
 
        var num = parseFloat(valueForParsing);

        if (!isNaN(num)) {
 
            var formattedWithDot = num.toFixed(2);
 
            var formattedWithComma = formattedWithDot.replace('.', ',');
            $this.val(formattedWithComma);
        } else {
             $this.val("");  
        }
    });

     $('body').on('paste', '.valor-decimal-br', function(event) {
        var $this = $(this);
         setTimeout(function() {
            var value = $this.val();
            var tempValue = "";
            var commaFound = false;
            for (var i = 0; i < value.length; i++) {
                var char = value[i];
                if (char >= '0' && char <= '9') {
                    tempValue += char;
                } else if (char === ',' && !commaFound) {
                    tempValue += char;
                    commaFound = true;
                }
            }
             var parts = tempValue.split(',');
            if (parts.length > 1 && parts[1].length > 2) {
                parts[1] = parts[1].substring(0, 2); 
                tempValue = parts.join(',');
            }
            $this.val(tempValue); 
        }, 0);
    });
});
</script>