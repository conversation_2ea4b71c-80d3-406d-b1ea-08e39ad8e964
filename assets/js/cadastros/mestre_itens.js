$(document).ready(function() {
    let eventoLoaded = false;
    let sistemasOrigemLoaded = false;
    let exIpiLoaded = false;
    let exIiLoaded = false;
    let prioridadesLoaded = false;
    let ncmsLoaded = false;
    let ownersLoaded = false;

    /**
     * Verifica se h  eventos selecionados e se sim, insere eles na selectpicker do evento
     * e atualiza a selectpicker com o comando refresh.
     */
    function inicializarEventosSelecionados() {
        if (eventosSelecionados && eventosSelecionados.length > 0) {
            $.each(eventosSelecionados, function(index, value) {
                if ($('#evento option[value="' + value + '"]').length === 0) {
                    $('#evento').append(`<option value="${value}" selected>${value}</option>`);
                }
            });
            $('#evento').selectpicker('refresh');
        }
    }

    /**
     * Verifica se h  Sistemas de Origem selecionados e se sim, insere eles na selectpicker
     * do sistema de origem e atualiza a selectpicker com o comando refresh.
     */
    function inicializarSistemasOrigemSelecionados() {
        if (sistemasOrigemSelecionados && sistemasOrigemSelecionados.length > 0) {
            $.each(sistemasOrigemSelecionados, function(index, value) {
                if ($('#sistema_origem_modal option[value="' + value + '"]').length === 0) {
                    $('#sistema_origem_modal').append(`<option value="${value}" selected>${value}</option>`);
                }
            });
            $('#sistema_origem_modal').selectpicker('refresh');
        }
    }


    /**
     * Verifica se h  Ex-IPIs selecionados e se sim, insere eles na selectpicker
     * do Ex-IPI e atualiza a selectpicker com o comando refresh.
     */
    function inicializarExIpiSelecionados() {
        if (exIpiSelecionados && exIpiSelecionados.length > 0) {
            exIpiSelecionados.forEach((exIpi) => {
                const [numExIpi, codNcm] = exIpi.split("|");
                const value = `${numExIpi}|${codNcm}`;
                const text = `${numExIpi} - ${codNcm}`;
                const option = `<option value="${value}" selected>${text}</option>`;
                $("#ex_ipi_modal").append(option);
            });
            $("#ex_ipi_modal").selectpicker("refresh");
        }
    }

    /**
     * Verifica se h  Ex-IIs selecionados e se sim, insere eles na selectpicker
     * do Ex-II e atualiza a selectpicker com o comando refresh.
     */
    function inicializarExIiSelecionados() {
        if (exIiSelecionados && exIiSelecionados.length > 0) {
            exIiSelecionados.forEach((exII) => {
                const [numExII, codNcm] = exII.split("|");
                const value = `${numExII}|${codNcm}`;
                const text = `${numExII} - ${codNcm}`;
                const option = `<option value="${value}" selected>${text}</option>`;
                $("#ex_ii_modal").append(option);
            });
            $("#ex_ii_modal").selectpicker("refresh");
        }
    }

    /**
     * Verifica se h  Prioridades selecionadas e se sim, carrega as Prioridades se n o estiverem carregadas
     * e marca as Prioridades que est o na lista de Selecionados.
     */
    function inicializarPrioridadesSelecionadas() {
        if (prioridadesSelecionadas && prioridadesSelecionadas.length > 0) {
            if (!prioridadesLoaded) {
                carregarPrioridades();
            } else {
                $('#prioridade').selectpicker('val', prioridadesSelecionadas);
                $('#prioridade').selectpicker('refresh');
            }
        }
    }

/**
 * Checks if there are selected NCMs and if so, loads the NCMs if they are not already loaded.
 * Updates the selectpicker with the list of selected NCMs.
 */
    function inicializarNcmSelecionados() {
        if (ncmsSelecionados && ncmsSelecionados.length > 0) {
            if (!ncmsLoaded) {
                carregarNcms();
            } else {
                $('#ncm_proposta_modal').selectpicker('val', ncmSelecionados);
                $('#ncm_proposta_modal').selectpicker('refresh');
            }
        }
    }

    /**
     * Verifica se h  propriet rios selecionados e se sim, carrega as Propriedades se n o estiverem carregadas
     * e marca as Propriedades que est o na lista de Selecionados.
     */
    function inicializarOwnerSelecionados() {
        if (ownersSelecionados && ownersSelecionados.length > 0) {
            if (!ownersLoaded) {
                carregarOwners();
            } else {
                $('#owner').selectpicker('val', ownersSelecionados);
                $('#owner').selectpicker('refresh');
            }
        }
    }

    /**
     * Carrega a lista de Eventos via AJAX e atualiza o selectpicker de Eventos.
     * Se j  tiver carregado, n o faz nada.
     * Se n o tiver carregado ainda, carrega a lista de Eventos e marca os eventos selecionados.
     */
    function carregarEventos() {
        if (eventoLoaded) return;

        $('#evento').empty().append('<option value="">Carregando...</option>');
        $('#evento').selectpicker('refresh');

        $.ajax({
            url: base_url + 'cadastros/mestre_itens/get_list_eventos',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                $('#evento').empty().append('<option value="sem_evento"><strong>Sem Evento/Pacote</strong></option>');

                if (response && response.length > 0) {
                    $.each(response, function(_, item) {
                        $('#evento').append(`<option value="${item.evento}">${item.evento}</option>`);
                    });
                }

                if (eventosSelecionados && eventosSelecionados.length > 0) {
                    $('#evento').selectpicker('val', eventosSelecionados);
                }

                $('#evento').selectpicker('refresh');
                eventoLoaded = true;
            },
            error: function(err) {
                console.error('Erro ao carregar Eventos:', err);
                $('#evento').empty().append('<option value="">Erro ao carregar</option>');
                $('#evento').selectpicker('refresh');
            }
        });
    }

    /**
     * Carrega a lista de Sistemas de Origem via AJAX e atualiza o selectpicker de Sistemas de Origem.
     * Se j  tiver carregado, n o faz nada.
     * Se n o tiver carregado ainda, carrega a lista de Sistemas de Origem e marca os sistemas de origem selecionados.
     */
    function carregarSistemasOrigem() {
        if (sistemasOrigemLoaded) return;

        $('#sistema_origem_modal').empty().append('<option value="">Carregando...</option>');
        $('#sistema_origem_modal').selectpicker('refresh');

        $.ajax({
            url: base_url + 'cadastros/mestre_itens/get_list_sistemas_origens',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                $('#sistema_origem_modal').empty().append('<option value="-1">Todos os sistemas de origem </option>');

                if (response && response.length > 0) {
                    $.each(response, function(_, sistemaOrigem) {
                        // Como response é um array de strings, usamos o valor diretamente
                        $('#sistema_origem_modal').append(`<option value="${sistemaOrigem}">${sistemaOrigem}</option>`);
                    });
                }

                if (sistemasOrigemSelecionados && sistemasOrigemSelecionados.length > 0) {
                    $('#sistema_origem_modal').selectpicker('val', sistemasOrigemSelecionados);
                }

                $('#sistema_origem_modal').selectpicker('refresh');
                sistemasOrigemLoaded = true;
            },
            error: function(err) {
                console.error('Erro ao carregar Sistemas de Origem:', err);
                $('#sistema_origem_modal').empty().append('<option value="">Erro ao carregar</option>');
                $('#sistema_origem_modal').selectpicker('refresh');
            }
        });
    }

    /**
     * Carrega a lista de Ex-IPI via AJAX e atualiza o selectpicker de Ex-IPI.
     * Se j  tiver carregado, n o faz nada.
     * Se n o tiver carregado ainda, carrega a lista de Ex-IPI e marca as Ex-IPIs selecionadas.
     */
    function carregarExIpi() {
        if (exIpiLoaded) return;

        $("#ex_ipi_modal").empty().append('<option value="-1">Carregando...</option>');
        $("#ex_ipi_modal").selectpicker("refresh");

        $.ajax({
            url: base_url + "cadastros/mestre_itens/get_list_ex_ipi_by_empresa",
            type: "GET",
            dataType: "json",
            success: function (response) {
            if (response && !response.error) {
                let options = '<option value="-1">Todos os Ex IPI</option>';

                response.forEach(function (exIpi) {
                let titulo = "";
                if (exIpi.titulo_ex) {
                    titulo = " - " + exIpi.titulo_ex;
                    if (titulo.length > 40) {
                    titulo = titulo.substring(0, 40) + "...";
                    }
                }

                const value = `${exIpi.num_ex_ipi}|${exIpi.cod_ncm}`;
                const text = `${exIpi.num_ex_ipi} - ${exIpi.cod_ncm}${titulo}`;
                const isSelected =
                    exIpiSelecionados && exIpiSelecionados.includes(value);

                options += `<option value="${value}" ${
                    isSelected ? "selected" : ""
                }>${text}</option>`;
                });

                $("#ex_ipi_modal").html(options);
                $("#ex_ipi_modal").selectpicker("refresh");
                exIpiLoaded = true;
            } else {
                console.error(
                "Erro ao carregar Ex IPI:",
                response ? response.message : "Erro desconhecido"
                );
            }
            },
            error: function (xhr, status, error) {
            console.error("Erro na requisição:", error);
            console.log("Resposta do servidor:", xhr.responseText);
            },
        });
    }

    /**
     * Loads the list of Ex-II options via AJAX and updates the Ex-II select picker.
     * If the list is already loaded, the function does nothing.
     * Otherwise, it will fetch the list from the server, populate the select picker,
     * and mark the previously selected Ex-IIs.
     * Handles errors by logging them to the console.
     */
    function carregarExII() {
        if (exIiLoaded) return;

        $("#ex_ii_modal").empty().append('<option value="-1">Carregando...</option>');
        $("#ex_ii_modal").selectpicker("refresh");

        $.ajax({
            url: base_url + "cadastros/mestre_itens/get_list_ex_ii_by_empresa",
            type: "GET",
            dataType: "json",
            success: function (response) {
            if (response && !response.error) {
                let options = '<option value="-1">Todos os Ex II</option>';

                response.forEach(function (exIi) {
                let titulo = "";
                if (exIi.titulo_ex) {
                    titulo = " - " + exIi.titulo_ex;
                    if (titulo.length > 40) {
                    titulo = titulo.substring(0, 40) + "...";
                    }
                }

                const value = `${exIi.num_ex_ii}|${exIi.cod_ncm}`;
                const text = `${exIi.num_ex_ii} - ${exIi.cod_ncm}${titulo}`;
                const isSelected =
                    exIiSelecionados && exIiSelecionados.includes(value);

                options += `<option value="${value}" ${
                    isSelected ? "selected" : ""
                }>${text}</option>`;
                });

                $("#ex_ii_modal").html(options);
                $("#ex_ii_modal").selectpicker("refresh");
                exIiLoaded = true;
            } else {
                console.error(
                "Erro ao carregar Ex II:",
                response ? response.message : "Erro desconhecido"
                );
            }
            },
            error: function (xhr, status, error) {
            console.error("Erro na requisição:", error);
            console.log("Resposta do servidor:", xhr.responseText);
            },
        });
    }

    /**
     * Loads the list of Prioridades options via AJAX and updates the Prioridades select picker.
     * If the list is already loaded, the function does nothing.
     * Otherwise, it will fetch the list from the server, populate the select picker,
     * and mark the previously selected Prioridades.
     * Handles errors by logging them to the console.
     */
    function carregarPrioridades() {
        if (prioridadesLoaded) return;

        $("#prioridade").empty().append('<option value="-1">Carregando...</option>');
        $("#prioridade").selectpicker("refresh");

        $.ajax({
            url: base_url + "cadastros/mestre_itens/get_list_prioridades_by_empresa",
            type: "GET",
            dataType: "json",
            success: function (response) {
            if (response && !response.error) {
                let options = '<option value="-1">Todas as Prioridades</option>';

                response.forEach(function (prioridade) {
                const value = prioridade.id_prioridade;
                const text = `${prioridade.nome}`;
                const isSelected =
                    prioridadesSelecionadas && prioridadesSelecionadas.includes(value);

                options += `<option value="${value}" ${
                    isSelected ? "selected" : ""
                }>${text}</option>`;
                });

                $("#prioridade").html(options);
                $("#prioridade").selectpicker("refresh");
                prioridadesLoaded = true;
            } else {
                console.error(
                "Erro ao carregar Prioridades:",
                response ? response.message : "Erro desconhecido"
                );
            }
            },
            error: function (xhr, status, error) {
            console.error("Erro na requisição:", error);
            console.log("Resposta do servidor:", xhr.responseText);
            },
        });
    }

    /**
     * Carrega a lista de NCMS via AJAX e atualiza o selectpicker de NCM Proposto.
     * Se j  tiver carregado, n o faz nada.
     * Se n o tiver carregado ainda, carrega a lista de NCMS e marca os NCMS selecionados.
     */
    function carregarNcms() {
        if (ncmsLoaded) return;

        $("#ncm_proposta_modal").empty().append('<option value="-1">Carregando...</option>');
        $("#ncm_proposta_modal").selectpicker("refresh");

        $.ajax({
            url: base_url + "cadastros/mestre_itens/get_list_ncms_by_empresa",
            type: "GET",
            dataType: "json",
            success: function (response) {
            if (response && !response.error) {
                console.log(response);
                let options = '<option value="-1">Todos os ncms propostos</option>';

                response.forEach(function (ncm) {
                    const value = ncm;
                    const isSelected = ncmsSelecionados && ncmsSelecionados.includes(value);

                    options += `<option value="${value}" ${
                        isSelected ? "selected" : ""
                    }>${value}</option>`;
                });

                $("#ncm_proposta_modal").html(options);
                $("#ncm_proposta_modal").selectpicker("refresh");
                ncmsLoaded = true;
            } else {
                console.error(
                "Erro ao carregar NCMS:",
                response ? response.message : "Erro desconhecido"
                );
            }
            },
            error: function (xhr, status, error) {
            console.error("Erro na requisição:", error);
            console.log("Resposta do servidor:", xhr.responseText);
            },
        });
    }

    function carregarOwners() {
        if (ownersLoaded) return;

        $("#owner").empty().append('<option value="-1">Carregando...</option>');
        $("#owner").selectpicker("refresh");

        $.ajax({
            url: base_url + "cadastros/mestre_itens/get_list_owners_by_empresa",
            type: "GET",
            dataType: "json",
            success: function (response) {
            if (response && !response.error) {
                let options = '<option value="-1">Todos os owners</option>';

                response.forEach(function (owner) {
                const value = owner.codigo;
                const text = `${owner.codigo} - ${owner.descricao}` + " - " + `${owner.nomes}`;
                const isSelected =
                    ownersSelecionados && ownersSelecionados.includes(value);

                options += `<option value="${value}" ${
                    isSelected ? "selected" : ""
                }>${text}</option>`;
                });

                $("#owner").html(options);
                $("#owner").selectpicker("refresh");
                ownersLoaded = true;
            } else {
                console.error(
                "Erro ao carregar Owners:",
                response ? response.message : "Erro desconhecido"
                );
            }
            },
            error: function (xhr, status, error) {
            console.error("Erro na requisição:", error);
            console.log("Resposta do servidor:", xhr.responseText);
            },
        });
    }

    inicializarEventosSelecionados();
    inicializarSistemasOrigemSelecionados();
    inicializarExIpiSelecionados();
    inicializarExIiSelecionados();
    inicializarPrioridadesSelecionadas();
    inicializarNcmSelecionados();
    inicializarOwnerSelecionados();
        
    $('#evento').on('show.bs.select', function() {
        carregarEventos();
    });

    $('#sistema_origem_modal').on('show.bs.select', function() {
        carregarSistemasOrigem();
    });

    $("#ex_ipi_modal").on("show.bs.select", function () {
        carregarExIpi();
    });

    $("#ex_ii_modal").on("show.bs.select", function () {
        carregarExII();
    });

    $('#prioridade').on('show.bs.select', function() {
        carregarPrioridades();
    });

    $('#ncm_proposta_modal').on('show.bs.select', function() {
        carregarNcms();
    });

    $('#owner').on('show.bs.select', function() {
        carregarOwners();
    });
});