// assets/js/new-layout.js
$(document).ready(function() {
    // Tenta obter variáveis globais definidas no HTML (pelo PHP)
    const BASE_URL = (typeof $('#base_url').val() !== 'undefined') ? $('#base_url').val() : '/';
    const SITE_URL = (typeof $('#site_url').val() !== 'undefined') ? $('#site_url').val() : BASE_URL; // Fallback para SITE_URL
    const SESS_USER_COMPANY_ID = (typeof $('#sess_user_company_id').val() !== 'undefined') ? $('#sess_user_company_id').val() : '';
    const SESS_USER_ID = (typeof $('#sess_user_id_js').val() !== 'undefined') ? $('#sess_user_id_js').val() : '';
    const CURRENT_PAGE_URI = (typeof $('#current_page_uri').val() !== 'undefined') ? $('#current_page_uri').val() : '';
    const CSRF_TOKEN_NAME = (typeof $('#csrf_token_name').val() !== 'undefined') ? $('#csrf_token_name').val() : '';
    const CSRF_HASH = (typeof $('#csrf_hash').val() !== 'undefined') ? $('#csrf_hash').val() : '';

    // Acessa a variável global gerada pelo PHP
    // É CRUCIAL que 'generatedMenuConfig' seja definida no seu arquivo PHP antes deste script.
    const menuConfig = (typeof generatedMenuConfig !== 'undefined') ? generatedMenuConfig : {};
    // É CRUCIAL que 'empresasDataFromPHP' seja definida no seu arquivo PHP antes deste script,
    // se você não for carregar as empresas via AJAX dentro de loadCompaniesIntoModalList.
    const empresasGlobalData = (typeof empresasDataFromPHP !== 'undefined') ? empresasDataFromPHP : [];


    // --- Funções Auxiliares ---
    function createIconHtml(item) {
        if (item.icon) {
            return `<i class="glyphicon ${item.icon}"></i> `;
        } else if (item.icon_fa) {
            return `<i class="fa ${item.icon_fa}"></i> `;
        } else if (item.icon_img) {
            return `<img src="${item.icon_img}" class="custom-list-icon" alt=""> `;
        }
        return '';
    }

    function createItemActionsHtml(item) {
        let actionsHtml = '';
        if (item.url && item.url !== '#') {
            actionsHtml = '<span class="item-actions">';
            if (typeof item.star !== 'undefined') { // Checa se a propriedade existe
                actionsHtml += `<i class="glyphicon ${item.star ? 'glyphicon-star' : 'glyphicon-star-empty'}" title="${item.star ? 'Favorito' : 'Não favorito'}"></i>`;
            }
            if (item.new_tab) {
                actionsHtml += `<i class="glyphicon glyphicon-export" title="Abrir em nova aba"></i>`;
            }
            actionsHtml += '</span>';
        }
        return actionsHtml;
    }

    // Função para popular uma coluna do modal
    function populateNavColumn(colId, items, parentKey = '', isCol2ForSections = false) {
        const $listGroup = $(`#${colId} .nav-list-group`);
        $listGroup.empty();
        if (!items || items.length === 0) {
            // $listGroup.append('<li class="nav-list-item text-muted">Nenhum item disponível.</li>');
            return;
        }

        items.forEach(item => {
            if (typeof item.is_visible !== 'undefined' && !item.is_visible) {
                return; // Pula este item se não for visível
            }

            let iconHtml = createIconHtml(item);
            let arrowHtml = (isCol2ForSections && item.sections) || (!isCol2ForSections && item.col3) || (item.col2IsSections) || (item.type === 'company_search') ? '<i class="fa fa-angle-right item-arrow"></i>' : '';
            let actionsHtml = createItemActionsHtml(item);

            const dataAttrs = `
                data-parent-key="${parentKey}"
                data-item-key="${item.key || (item.label ? item.label.replace(/\s+/g, '-').toLowerCase() : '')}"
                ${(isCol2ForSections && item.sections) || item.col2IsSections ? `data-is-section="true"` : ''}
                ${item.url ? `data-url="${item.url}"` : ''}
                ${item.new_tab ? `data-new-tab="true"` : ''}
            `;

            let listItemHtml;
            if (item.type === 'company_search') {
                 listItemHtml = `
                    <li class="nav-list-item non-interactive">
                        <input type="text" id="modal-company-filter-input" class="form-control nav-filter-input" placeholder="Digitar o nome da empresa...">
                    </li>
                    <div id="modal-empresas-list-container" class="nav-list-group" style="padding:0;">
                        </div>
                `;
                 $listGroup.append(listItemHtml); // Adiciona o input e o container
                 loadCompaniesIntoModalList();    // Carrega as empresas
                 $('#modal-company-filter-input').off('keyup').on('keyup', filterCompaniesInModal); // Garante um único handler
                 return; // Continua para o próximo item, pois este é especial

            } else if (item.url && item.url !== '#' && !isCol2ForSections && !item.col3 && !item.col2IsSections) { // Link final
                listItemHtml = `
                    <li class="nav-list-item nav-link-item" ${dataAttrs}>
                        <a href="${item.url}" ${item.new_tab ? 'target="_blank" rel="noopener noreferrer"' : ''}>
                            ${iconHtml}
                            <span class="item-label">${item.label}</span>
                            ${actionsHtml}
                        </a>
                    </li>`;
            } else { // Item que abre próximo nível ou é seção
                listItemHtml = `
                    <li class="nav-list-item" ${dataAttrs}>
                        ${iconHtml}
                        <span class="item-label">${item.label}</span>
                        ${arrowHtml}
                        ${actionsHtml}
                    </li>`;
            }
            $listGroup.append(listItemHtml);
        });
    }

    // Popula a Coluna 1 (Itens Principais do Modal)
    function initializeModalNav() {
        const col1Items = Object.keys(menuConfig)
            .filter(key => menuConfig[key] && !menuConfig[key].directUrl && (typeof menuConfig[key].is_visible === 'undefined' || menuConfig[key].is_visible))
            .map(key => ({
                key: key,
                label: menuConfig[key].title,
                icon: menuConfig[key].icon,
                icon_fa: menuConfig[key].icon_fa,
                icon_img: menuConfig[key].icon_img,
                col2: menuConfig[key].col2,
                col2IsSections: menuConfig[key].col2IsSections, // Se os itens da col2 são seções para col3
                sections: menuConfig[key].sections, // Se a col2 é composta por seções (ex: Gerenciar)
                type: menuConfig[key].type
            }));
        populateNavColumn('nav-col-1', col1Items, '', false);
    }


    // --- Event Handlers ---
    let activeCol1ItemKey = null;
    let activeCol2ItemKey = null;

    // Clicar em item da Coluna 1
    $('#nav-list-col1').on('click', '.nav-list-item:not(.non-interactive)', function() {
        const $this = $(this);
        activeCol1ItemKey = $this.data('item-key');
        const config = menuConfig[activeCol1ItemKey];

        $('#nav-list-col1 .nav-list-item').removeClass('active');
        $this.addClass('active');

        $('#nav-col-2, #nav-col-3').hide();
        $('#nav-list-col2, #nav-list-col3').empty();
        $('#nav-col-title2, #nav-col-title3').text('');

        if (config) {
            $('#nav-col-title2').text(config.title); // Título da Coluna 2 é o título do item da Coluna 1
            if (config.col2IsSections && config.sections) {
                const sectionItems = Object.keys(config.sections)
                    .filter(key => typeof config.sections[key].is_visible === 'undefined' || config.sections[key].is_visible)
                    .map(key => ({
                        key: key, // Chave da seção, ex: 'administracao'
                        label: config.sections[key].title,
                        icon: config.sections[key].icon,
                        icon_fa: config.sections[key].icon_fa,
                        icon_img: config.sections[key].icon_img,
                        col3: config.sections[key].col3, // Itens para a coluna 3
                        is_visible: typeof config.sections[key].is_visible === 'undefined' ? true : config.sections[key].is_visible
                    }));
                populateNavColumn('nav-col-2', sectionItems, activeCol1ItemKey, true); // true indica que col2 é para seções
                $('#nav-col-2').show();
            } else if (config.col2) {
                const col2Items = config.col2
                    .filter(item => typeof item.is_visible === 'undefined' || item.is_visible)
                    .map(item => ({ ...item, key: (item.label ? item.label.replace(/\s+/g, '-').toLowerCase() : Math.random().toString(36).substring(7)) }));
                populateNavColumn('nav-col-2', col2Items, activeCol1ItemKey, false);
                $('#nav-col-2').show();
            }
        }
    });

    // Clicar em item da Coluna 2
    $('#nav-list-col2').on('click', '.nav-list-item:not(.non-interactive)', function() {
        const $this = $(this);
        const parentKeyCol1 = $this.data('parent-key'); // Ex: "gerenciar"
        activeCol2ItemKey = $this.data('item-key');   // Ex: "administracao"

        // Se for um link final (tem <a> filho direto)
        if ($this.hasClass('nav-link-item')) {
            if ($this.find('a').attr('target') !== '_blank') {
                 $('#mainNavigationModal').modal('hide');
            }
            return; // Navegação ocorre pelo link
        }

        const sectionConfig = menuConfig[parentKeyCol1]?.sections?.[activeCol2ItemKey];

        $('#nav-list-col2 .nav-list-item').removeClass('active');
        $this.addClass('active');
        $('#nav-col-3').hide();
        $('#nav-list-col3').empty();
        $('#nav-col-title3').text('');

        if (sectionConfig && sectionConfig.col3) {
            $('#nav-col-title3').text(sectionConfig.title);
            const col3Items = sectionConfig.col3
                .filter(item => typeof item.is_visible === 'undefined' || item.is_visible)
                .map(item => ({ ...item, key: (item.label ? item.label.replace(/\s+/g, '-').toLowerCase() : Math.random().toString(36).substring(7)) }));
            populateNavColumn('nav-col-3', col3Items, activeCol2ItemKey, false);
            $('#nav-col-3').show();
        }
    });

     // Clicar em item da Coluna 3 (links finais)
    $('#nav-list-col3').on('click', '.nav-list-item.nav-link-item', function() {
        if ($(this).find('a').attr('target') !== '_blank') {
            $('#mainNavigationModal').modal('hide');
        }
        // A navegação é feita pelo próprio link <a>
    });

    // Abrir Modal pelos Ícones da Sidebar ou Botão Principal de Menu
    $('.sidebar-icon-trigger, #main-menu-trigger').on('click', function(e) {
        e.preventDefault();
        const menuKey = $(this).data('menu-key');
        const config = menuKey ? menuConfig[menuKey] : null;

        $('.sidebar-icon-trigger').removeClass('active');
        if ($(this).hasClass('sidebar-icon-trigger')) {
             $(this).addClass('active');
        }

        if (config && config.directUrl) {
            window.location.href = config.directUrl;
        } else {
            initializeModalNav();
            $('#mainNavigationModal').modal('show');

            if (menuKey && menuConfig[menuKey] && !menuConfig[menuKey].directUrl) {
                $('#nav-list-col1 .nav-list-item[data-menu-key="' + menuKey + '"]').trigger('click');
            } else {
                $('#nav-col-2, #nav-col-3').hide();
                $('#nav-list-col1 .nav-list-item').removeClass('active');
            }
        }
    });

    // Filtro na Coluna 1 do Modal
    $('#nav-modal-filter').on('keyup', function() {
        const filterText = $(this).val().toLowerCase();
        $('#nav-list-col1 .nav-list-item').each(function() {
            const itemText = $(this).find('.item-label').text().toLowerCase();
            $(this).toggle(itemText.includes(filterText));
        });
    });

    // Limpar estado do modal ao fechar
    $('#mainNavigationModal').on('hidden.bs.modal', function () {
        $('#nav-list-col1 .nav-list-item').removeClass('active').show();
        $('#nav-col-2, #nav-col-3').hide().find('.nav-list-group').empty();
        $('#nav-col-title2, #nav-col-title3').text('');
        $('#nav-modal-filter').val('');
        $('.sidebar-icon-trigger').removeClass('active');
        activeCol1ItemKey = null;
        activeCol2ItemKey = null;
    });

    // Função para carregar e listar empresas no modal
    function loadCompaniesIntoModalList() {
        const $listContainer = $('#modal-empresas-list-container');
        if (!$listContainer.length) return; // Container não existe
        $listContainer.html('<li class="nav-list-item text-muted text-center"><i class="fa fa-spinner fa-spin"></i> Carregando...</li>');

        // Usar empresasGlobalData definida no PHP
        let empresasHtml = '';
        if (empresasGlobalData && empresasGlobalData.length > 0) {
            empresasGlobalData.forEach(emp => {
                const isCurrent = emp.id == SESS_USER_COMPANY_ID;
                empresasHtml += `
                    <a href="${SITE_URL}home/change_company/${emp.id}" class="modal-empresa-item list-group-item list-group-item-action ${isCurrent ? 'active' : ''}">
                        ${emp.nome}
                        ${isCurrent ? '<i class="glyphicon glyphicon-ok float-right"></i>' : ''}
                    </a>`;
            });
             $listContainer.html(empresasHtml);
        } else {
            $listContainer.html('<li class="nav-list-item text-muted text-center">Nenhuma empresa disponível.</li>');
        }
         // Adicionar handler de clique para fechar modal ao selecionar empresa
        $listContainer.find('.modal-empresa-item').on('click', function() {
            $('#mainNavigationModal').modal('hide');
        });
    }

    // Função para filtrar empresas no modal
    function filterCompaniesInModal() {
        const filterText = $('#modal-company-filter-input').val().toLowerCase();
        $('#modal-empresas-list-container .modal-empresa-item').each(function() {
            const companyName = $(this).text().toLowerCase();
            $(this).toggle(companyName.includes(filterText));
        });
    }

    // Atualizar contador de pendências na sidebar
    function updateSidebarBadge() {
        // Esta função deve ser chamada se você tiver um endpoint para buscar o número de pendências
        // Exemplo:
        /*
        $.ajax({
            url: SITE_URL + 'home/ajax_get_pendencias_count', // Crie este endpoint
            type: 'GET',
            dataType: 'json', // Espera um JSON com uma propriedade como 'count'
            success: function(response) {
                if (response && response.count && parseInt(response.count) > 0) {
                    $('#sidebar_total_to_answer').text(response.count).show();
                } else {
                    $('#sidebar_total_to_answer').text('').hide();
                }
            },
            error: function() {
                $('#sidebar_total_to_answer').text('').hide();
            }
        });
        */
        // Por agora, vou deixar comentado, já que a lógica original buscava HTML para popular um lugar específico.
        // Se você tem o número de pendências disponível via PHP no carregamento da página,
        // você pode passá-lo para uma variável JS e usá-lo aqui, ou chamar um AJAX dedicado.
    }
    // updateSidebarBadge(); // Chame se tiver o AJAX implementado

    // Marcar o ícone da sidebar correspondente à página atual
    function setActiveSidebarIcon() {
        let currentKey = null;
        // Tenta encontrar uma correspondência direta com directUrl
        for (const key in menuConfig) {
            if (menuConfig.hasOwnProperty(key) && menuConfig[key].directUrl) {
                // Remove query strings e hashes para comparação mais simples da URL base
                let cleanDirectUrl = menuConfig[key].directUrl.split('?')[0].split('#')[0];
                let cleanCurrentUrl = window.location.href.split('?')[0].split('#')[0];

                if (cleanCurrentUrl === cleanDirectUrl || cleanCurrentUrl === cleanDirectUrl + '/') {
                    currentKey = key;
                    break;
                }
                // Verificação adicional para URIs do CodeIgniter
                if (menuConfig[key].directUrl.includes(CURRENT_PAGE_URI) && CURRENT_PAGE_URI !== '') {
                     currentKey = key;
                     break;
                }
            }
        }
        
        if (!currentKey && (CURRENT_PAGE_URI === '' || CURRENT_PAGE_URI === 'home' || CURRENT_PAGE_URI === 'index.php/home')) {
            currentKey = 'home';
        }

        if (currentKey) {
            $('.sidebar-icon-trigger[data-menu-key="' + currentKey + '"]').addClass('active');
        }
    }
    setActiveSidebarIcon();

});  