/* assets/css/new-layout.css */
body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* Exemplo de fonte, ajuste */
    background-color: #F7F8FA; /* Um cinza bem claro para o fundo geral */
    display: flex;
    min-height: 100vh;
    padding-top: 50px; /* Para o alerta de homologação, se existir */
}

.homologation-alert {
    background-color: #fcf8e3;
    color: #8a6d3b;
    border: 1px solid #faebcc;
    padding: 10px 15px;
    text-align: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 2000; /* Bem alto */
    font-size: 14px;
}

/* 1. Barra Lateral de Ícones Fixa */
#app-sidebar {
    width: 70px; /* Largura da barra de ícones */
    background-color: #071D38; /* Azul bem escuro do Figma */
    color: #fff;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 15px;
    z-index: 1050; /* Alto para ficar sobre o conteúdo */
}
body.has-homologation-alert #app-sidebar {
    top: 50px; /* Altura do alerta */
    height: calc(100vh - 50px);
}

#app-sidebar .sidebar-logo {
    margin-bottom: 30px;
}
#app-sidebar .sidebar-logo img {
    width: 40px; /* Ajuste conforme seu logo vertical */
    height: auto;
}

#app-sidebar .sidebar-nav-icons {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
}
#app-sidebar .sidebar-nav-icons li a.sidebar-icon-trigger {
    display: block;
    color: #A7B4C1; /* Cinza claro para ícones inativos */
    padding: 15px 0;
    text-align: center;
    font-size: 20px; /* Ajuste para o tamanho dos ícones */
    text-decoration: none;
    position: relative;
}
#app-sidebar .sidebar-nav-icons li a.sidebar-icon-trigger:hover,
#app-sidebar .sidebar-nav-icons li a.sidebar-icon-trigger.active {
    color: #FFFFFF; /* Branco para ícone ativo/hover */
    background-color: #15355C; /* Azul um pouco mais claro para fundo ativo */
}
/* Indicador de item ativo (opcional, um pequeno traço) */
#app-sidebar .sidebar-nav-icons li a.sidebar-icon-trigger.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 25px;
    background-color: #FFFFFF; /* Cor do indicador */
}
#app-sidebar .sidebar-nav-icons li a.sidebar-icon-trigger .custom-icon {
    width: 20px; /* Ajuste para ícones customizados como o da Diana */
    height: auto;
    filter: grayscale(100%) brightness(1.5); /* Efeito para inativo */
}
#app-sidebar .sidebar-nav-icons li a.sidebar-icon-trigger:hover .custom-icon,
#app-sidebar .sidebar-nav-icons li a.sidebar-icon-trigger.active .custom-icon {
    filter: none; /* Remove filtro no hover/active */
}


#app-sidebar .sidebar-bottom-actions {
    margin-top: auto;
    padding-bottom: 10px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
#app-sidebar .sidebar-bottom-actions a.sidebar-icon-bottom {
    color: #A7B4C1;
    padding: 10px 0;
    font-size: 18px;
    text-decoration: none;
    position: relative;
}
#app-sidebar .sidebar-bottom-actions a.sidebar-icon-bottom:hover {
    color: #FFFFFF;
}
#app-sidebar .sidebar-bottom-actions .badge {
    position: absolute;
    top: 5px;
    right: 18px; /* Ajuste para centralizar melhor na barra estreita */
    font-size: 9px;
    padding: 2px 4px;
    background-color: #D9534F; /* Cor do badge */
}

/* 2. Conteúdo Principal da Aplicação */
#app-main-content {
    margin-left: 70px; /* Mesma largura da sidebar */
    width: calc(100% - 70px);
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* Ocupa toda a altura da viewport */
}
body.has-homologation-alert #app-main-content {
    padding-top: 50px; /* Altura do alerta */
    min-height: calc(100vh - 50px);
}


/* Barra de Cabeçalho Superior */
#app-header {
    height: 60px;
    background-color: #FFFFFF; /* Header branco conforme Figma */
    border-bottom: 1px solid #E5E9F2; /* Borda inferior sutil */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 25px;
    color: #333;
    position: sticky; /* Fixa o header no topo da área de scroll do #app-main-content */
    top: 0;
    z-index: 1020; /* Abaixo da sidebar, acima do conteúdo da página */
}
body.has-homologation-alert #app-header {
    top: 50px; /* Considera o alerta */
}


#app-header .header-left {
    display: flex;
    align-items: center;
}
#app-header .header-left #main-menu-trigger {
    color: #071D38; /* Cor do ícone de menu */
    margin-right: 15px;
    padding: 5px 10px;
}
#app-header .header-left .page-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #071D38;
}

#app-header .header-right {
    display: flex;
    align-items: center;
}
#app-header .client-logo-header img {
    max-height: 35px; /* Ajuste */
    margin-right: 20px;
}
#app-header .user-profile-dropdown .dropdown-toggle {
    color: #333;
    text-decoration: none;
    display: flex;
    align-items: center;
    font-size: 14px;
}
#app-header .user-profile-dropdown .dropdown-toggle i.glyphicon-user {
    font-size: 16px;
    margin-right: 8px;
    color: #071D38;
}
#app-header .user-profile-dropdown .dropdown-toggle i.fa-angle-down {
    margin-left: 5px;
}
#app-header .user-profile-dropdown .dropdown-menu {
    margin-top: 8px; /* Pequeno espaço */
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
#app-header .user-profile-dropdown .dropdown-menu > li > a {
    padding: 8px 15px;
    font-size: 14px;
}
#app-header .user-profile-dropdown .dropdown-menu > li > a i {
    margin-right: 8px;
}


/* Área de Conteúdo da Página */
#page-content-area {
    flex-grow: 1; /* Faz com que o conteúdo ocupe o espaço restante */
    padding: 25px; /* Espaçamento interno */
    background-color: #F7F8FA; /* Fundo da área de conteúdo */
}

/* Rodapé */
#app-footer {
    height: 50px;
    background-color: #FFFFFF; /* Footer branco */
    border-top: 1px solid #E5E9F2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 25px;
    font-size: 13px;
    color: #555;
}
#app-footer .footer-copy {
    color: #071D38;
}
#app-footer .footer-version {
     color: #5A6B7F;
}
#app-footer .footer-social a {
    color: #5A6B7F;
    margin-left: 10px;
    font-size: 16px;
}
#app-footer .footer-social a:hover {
    color: #071D38;
}


/* 3. Modal de Navegação Principal */
#mainNavigationModal .modal-dialog {
    margin-top: 30px; /* Espaço do topo */
}
#mainNavigationModal .modal-content {
    border-radius: 8px; /* Bordas arredondadas como no Figma */
    border: none;
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
}
#mainNavigationModal .modal-header {
    border-bottom: 1px solid #E5E9F2;
    padding: 10px 20px; /* Menor padding */
}
#mainNavigationModal .modal-header .close {
    font-size: 28px;
    opacity: 0.5;
}
#mainNavigationModal .modal-body {
    padding: 20px;
    min-height: 500px; /* Altura mínima para visualização */
    background-color: #FDFEFF; /* Fundo do corpo do modal */
}

#mainNavigationModal .nav-col {
    /* padding: 0 10px; */ /* Espaçamento entre colunas, se necessário */
    /* border-right: 1px solid #E5E9F2; */ /* Linha divisória opcional */
}
#mainNavigationModal .nav-col:last-child {
    border-right: none;
}

#mainNavigationModal .nav-filter-input {
    margin-bottom: 15px;
    border-radius: 4px;
    font-size: 14px;
}

#mainNavigationModal .nav-list-group {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 420px; /* Altura para scroll */
    overflow-y: auto;
}

/* Estilo dos itens da lista no modal */
#mainNavigationModal .nav-list-group .nav-list-item {
    padding: 10px 15px;
    font-size: 14px;
    color: #334155; /* Cor do texto do item */
    border-bottom: 1px solid #F1F5F9; /* Borda sutil entre itens */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease, color 0.2s ease;
}
#mainNavigationModal .nav-list-group .nav-list-item i:first-child { /* Ícone à esquerda */
    margin-right: 10px;
    color: #64748B; /* Cor do ícone */
}
#mainNavigationModal .nav-list-group .nav-list-item .item-arrow { /* Seta à direita */
    color: #94A3B8;
}

#mainNavigationModal .nav-list-group .nav-list-item:last-child {
    border-bottom: none;
}

#mainNavigationModal .nav-list-group .nav-list-item:hover,
#mainNavigationModal .nav-list-group .nav-list-item.active {
    background-color: #EFF6FF; /* Azul bem claro para hover/ativo */
    color: #071D38; /* Cor do texto ativa */
    font-weight: 500;
}
#mainNavigationModal .nav-list-group .nav-list-item.active i {
    color: #071D38; /* Cor do ícone ativo */
}
#mainNavigationModal .nav-list-group .nav-list-item.active .item-arrow {
    transform: rotate(90deg); /* Gira a seta para indicar expansão (se aplicável) */
}


/* Links finais (Coluna 2 ou 3) */
#mainNavigationModal .nav-list-group .nav-link-item a {
    display: block;
    text-decoration: none;
    color: inherit; /* Herda a cor do .nav-list-item */
    width: 100%; /* Ocupa todo o li */
}
#mainNavigationModal .nav-list-group .nav-link-item .item-actions { /* Ícones de estrela/nova aba */
    margin-left: auto; /* Empurra para a direita */
    display: flex;
    align-items: center;
}
#mainNavigationModal .nav-list-group .nav-link-item .item-actions i {
    margin-left: 8px;
    color: #94A3B8;
    font-size: 13px;
}
#mainNavigationModal .nav-list-group .nav-link-item:hover .item-actions i,
#mainNavigationModal .nav-list-group .nav-link-item.active .item-actions i {
     color: #071D38;
}

#mainNavigationModal .nav-col-title {
    font-size: 13px; /* Menor que o título da página */
    font-weight: 600;
    color: #475569; /* Cinza escuro para o título da coluna */
    padding-bottom: 8px;
    margin-bottom: 10px;
    border-bottom: 1px solid #E2E8F0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Estilo para a busca de empresas dentro do modal */
#mainNavigationModal #modal-empresas-list-container {
    margin-top: 10px;
    max-height: 300px; /* Altura para scroll */
    overflow-y: auto;
    border: 1px solid #E2E8F0;
    border-radius: 4px;
}
#mainNavigationModal .modal-empresa-item {
    display: block;
    padding: 8px 12px;
    font-size: 14px;
    color: #334155;
    text-decoration: none;
    border-bottom: 1px solid #F1F5F9;
}
#mainNavigationModal .modal-empresa-item:last-child {
    border-bottom: none;
}
#mainNavigationModal .modal-empresa-item:hover {
    background-color: #EFF6FF;
    color: #071D38;
}
#mainNavigationModal .modal-empresa-item .glyphicon-ok {
    color: #10B981; /* Verde para empresa selecionada */
    margin-left: auto;
}

/* Ajustes de responsividade básicos (Exemplo) */
@media (max-width: 768px) {
    #app-sidebar {
        width: 50px; /* Pode reduzir ainda mais a sidebar */
    }
    #app-sidebar .sidebar-logo img { width: 30px; }
    #app-sidebar .sidebar-nav-icons li a.sidebar-icon-trigger { font-size: 18px; padding: 12px 0;}

    #app-main-content {
        margin-left: 50px;
        width: calc(100% - 50px);
    }
    #app-header .header-left .page-title { font-size: 16px; }
    #app-header .client-logo-header img { max-height: 30px; }
    #app-header .user-profile-dropdown .dropdown-toggle span { display: none; } /* Esconde nome em telas pequenas */

    #mainNavigationModal .modal-xl { max-width: 95%; } /* Modal mais largo em telas pequenas */
    #mainNavigationModal .row > .nav-col {
        /* Em telas pequenas, as colunas do modal podem precisar empilhar ou ter scroll horizontal */
        margin-bottom: 15px;
    }
}